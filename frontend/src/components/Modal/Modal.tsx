import React, { ReactNode, useRef } from 'react';
import clsx from 'clsx';
import styles from './Modal.module.scss';
import { IconSvgObject } from '@hugeicons/react';
import { MultiplicationSignIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { useBreakpoint } from '@/utils/breakpointUtils';

interface ModalProps extends React.HTMLAttributes<HTMLDivElement> {
  open: boolean;
  onClose: () => void;
  children: ReactNode;
  actionButtons?: ReactNode;
  title?: string;
  closeButtonPosition?: 'left' | 'right';
}

export const Modal: React.FC<ModalProps> = ({
  open,
  onClose,
  children,
  actionButtons,
  title,
  closeButtonPosition = 'left',
  ...props
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useBreakpoint();

  if (!open) return null;

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className={styles.overlay} onClick={handleOverlayClick}>
      <div
        className={clsx(styles.modal, { [styles.mobile]: isMobile })}
        role="dialog"
        aria-modal="true"
        {...props}
      >
        <div className={styles.modalInner}>
          <div className={styles.header}>
            {title && <h2 className={styles.title}>{title}</h2>}
            <button
              className={clsx({
                [styles.closeButtonRight]: closeButtonPosition === 'right',
                [styles.closeButtonLeft]: closeButtonPosition === 'left',
              })}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Close button clicked');
                onClose();
              }}
              aria-label="Close modal"
            >
              <HugeiconsIcon icon={MultiplicationSignIcon as unknown as IconSvgObject} size={24} />
            </button>
          </div>
          <div className={styles.content} ref={contentRef}>
            {children}
          </div>
        </div>
        {actionButtons && <div className={styles.actions}>{actionButtons}</div>}
      </div>
    </div>
  );
};
